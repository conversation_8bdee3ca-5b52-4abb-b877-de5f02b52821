{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "regions": ["bom1"], "env": {"ADMIN_PASSWORD": "@admin_password", "JWT_SECRET": "@jwt_secret", "NEXTAUTH_SECRET": "@nextauth_secret", "SESSION_TIMEOUT": "24h", "MAX_LOGIN_ATTEMPTS": "5", "LOCKOUT_DURATION": "15m"}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/admin/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}], "rewrites": [{"source": "/admin", "destination": "/admin"}]}