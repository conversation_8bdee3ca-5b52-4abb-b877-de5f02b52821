# 🎓 IIT Palakkad Research Lab Website

A modern, responsive website for IIT Palakkad Research Laboratory with a comprehensive admin panel for content management.

## ✨ Features

### 🌐 **Public Website**
- **Responsive Design**: Optimized for all devices (mobile, tablet, desktop)
- **Modern UI**: Clean and professional interface with Tailwind CSS
- **Fast Performance**: Built with Next.js 15 for optimal speed and SEO
- **Accessibility**: WCAG compliant design
- **Research Showcase**: Publications, team, facilities, and achievements
- **Event Management**: Upcoming events and announcements
- **Contact Integration**: Easy contact forms and information

### 🔐 **Admin Panel** (`/admin`)
- **Secure Authentication**: Password-protected admin access
- **Complete Content Management**: Edit all website content
- **Real-time Updates**: Changes reflect immediately on the public site
- **User-friendly Interface**: Intuitive forms and navigation
- **Data Persistence**: All changes saved automatically
- **Media Management**: Upload and organize images

#### **Admin Sections:**
- 🏠 **Home**: Hero content, mission, vision, statistics, news
- ℹ️ **About**: Lab information, history, photo sections
- 👥 **Team**: Faculty, students, interns management
- 🔬 **Research**: Publications, papers, research areas
- 🏭 **Facilities**: Laboratory equipment and instruments
- 🏆 **Achievements**: Awards, recognitions, milestones
- 📅 **Events**: Conferences, workshops, seminars
- 🤝 **Join Us**: Opportunities, FAQs, application process
- 📞 **Contact**: Address, phone, email, office hours
- 🖼️ **Media**: Image gallery and media assets

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript for type safety
- **Styling**: Tailwind CSS for modern design
- **UI Components**: Radix UI primitives
- **Icons**: Lucide React
- **Authentication**: Custom JWT-based auth
- **Data Storage**: localStorage (production-ready)
- **Deployment**: Vercel-optimized

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. **Clone the repository:**
```bash
git clone <repository-url>
cd iit-palakad-web
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment variables:**
```bash
cp .env.example .env.local
# Edit .env.local with your admin password and secrets
```

4. **Run development server:**
```bash
npm run dev
```

5. **Open in browser:**
   - Public site: [http://localhost:3000](http://localhost:3000)
   - Admin panel: [http://localhost:3000/admin](http://localhost:3000/admin)

## 📁 Project Structure

```
├── app/                    # Next.js app directory
│   ├── admin/             # Admin panel pages
│   ├── api/               # API routes for authentication
│   ├── globals.css        # Global styles
│   └── layout.tsx         # Root layout
├── components/            # Reusable components
│   ├── admin/            # Admin panel components
│   ├── ui/               # UI primitives
│   └── sections/         # Page sections
├── lib/                  # Utility functions
│   ├── auth.ts           # Authentication logic
│   ├── data-context.tsx  # Data management
│   └── utils.ts          # Helper functions
├── public/               # Static assets
├── vercel.json          # Vercel deployment config
└── DEPLOYMENT.md        # Deployment guide
```

## 🌐 Production Deployment

### **GitHub + Vercel Deployment (Recommended)**

**Repository**: [https://github.com/Gopikrish-30/iit-palakkad-arise](https://github.com/Gopikrish-30/iit-palakkad-arise)

1. **Deploy to GitHub:**
```bash
# Run GitHub deployment script (Windows)
deploy-github.bat

# Or run GitHub deployment script (Linux/Mac)
chmod +x deploy-github.sh
./deploy-github.sh
```

2. **Set up Vercel integration:**
   - Connect Vercel to your GitHub repository
   - Configure environment variables in Vercel dashboard
   - Enable automatic deployments

3. **Environment variables for Vercel:**
```env
ADMIN_PASSWORD=YourSecurePassword123!
JWT_SECRET=your-32-character-jwt-secret-key
NEXTAUTH_SECRET=your-32-character-nextauth-secret
SESSION_TIMEOUT=24h
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=15m
```

### **Alternative: Direct Vercel Deployment**

1. **Prepare for deployment:**
```bash
# Run deployment script (Windows)
deploy.bat

# Or run deployment script (Linux/Mac)
chmod +x deploy.sh
./deploy.sh
```

2. **Deploy to Vercel:**
```bash
# Install Vercel CLI
npm i -g vercel

# Login and deploy
vercel login
vercel --prod
```

📖 **Deployment guides**:
- [GITHUB-DEPLOYMENT.md](./GITHUB-DEPLOYMENT.md) - GitHub + Vercel setup
- [DEPLOYMENT.md](./DEPLOYMENT.md) - Direct Vercel deployment

## 🔐 Admin Access

- **URL**: `https://yourdomain.com/admin`
- **Authentication**: Password-based with JWT tokens
- **Session**: 24-hour default timeout
- **Security**: Rate limiting, secure headers, CSRF protection

## 📝 Content Management

### **Adding New Content**
1. Navigate to `/admin`
2. Login with admin credentials
3. Select the section to edit
4. Use the intuitive forms to add/edit content
5. Changes are saved automatically and appear live

### **Supported Content Types**
- Team members (faculty, students, interns)
- Research publications (journal, conference, book chapters)
- Laboratory facilities and equipment
- Awards and achievements
- Events and announcements
- Join us opportunities and FAQs
- Contact information and office hours

## 🎨 Customization

### **Styling**
- Modify `app/globals.css` for global styles
- Edit Tailwind classes in components
- Customize color scheme in `tailwind.config.js`

### **Content Structure**
- Edit initial data in `lib/data-context.tsx`
- Modify component layouts in `components/sections/`
- Update admin forms in `components/admin/`

## 🔧 Development

### **Available Scripts**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks
```

### **Code Quality**
- TypeScript for type safety
- ESLint for code quality
- Prettier for code formatting
- Responsive design testing

## 🐛 Troubleshooting

### **Common Issues**
1. **Admin login fails**: Check environment variables
2. **Build errors**: Run `npm run type-check`
3. **Styling issues**: Clear browser cache
4. **Data not saving**: Check localStorage permissions

### **Getting Help**
- Check the [DEPLOYMENT.md](./DEPLOYMENT.md) guide
- Review Vercel deployment logs
- Ensure all environment variables are set

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

---

**🚀 Ready for production deployment!** Follow the [DEPLOYMENT.md](./DEPLOYMENT.md) guide to go live.
